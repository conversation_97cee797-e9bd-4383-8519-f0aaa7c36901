#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面模块
实现XY商品采集器的主要用户界面
"""

import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QSpinBox, QCheckBox, QComboBox,
    QTableWidget, QTableWidgetItem, QProgressBar, QStatusBar,
    QGroupBox, QSplitter, QHeaderView, QMessageBox, QFileDialog,
    QApplication, QFrame, QTableView
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from utils.logger import get_logger
from core.collector_engine import CollectorEngine
from core.data_processor import DataProcessor, ProductDataModel
from core.export_manager import ExportManager


class CollectionThread(QThread):
    """采集线程"""

    progress_updated = pyqtSignal(dict)
    data_collected = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, collector_engine, collection_type, **kwargs):
        super().__init__()
        self.collector_engine = collector_engine
        self.collection_type = collection_type
        self.kwargs = kwargs
        self.logger = get_logger(self.__class__.__name__)

    def run(self):
        """运行采集任务"""
        try:
            if self.collection_type == 'search':
                data = self.collector_engine.collect_search_data(
                    progress_callback=self.progress_updated.emit,
                    **self.kwargs
                )
            elif self.collection_type == 'shop':
                data = self.collector_engine.collect_shop_data(
                    progress_callback=self.progress_updated.emit,
                    **self.kwargs
                )
            else:
                raise ValueError(f"未知的采集类型: {self.collection_type}")

            self.data_collected.emit(data)

        except Exception as e:
            self.logger.error(f"采集线程异常: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.finished.emit()


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, auth_manager, config_manager):
        """
        初始化主窗口
        
        Args:
            auth_manager: 授权管理器
            config_manager: 配置管理器
        """
        super().__init__()
        self.auth_manager = auth_manager
        self.config_manager = config_manager
        self.logger = get_logger(self.__class__.__name__)
        
        # 数据存储
        self.collected_data = []
        self.is_collecting = False

        # 初始化组件
        self.collector_engine = CollectorEngine(config_manager)
        self.data_processor = DataProcessor()
        self.data_model = ProductDataModel()
        self.export_manager = ExportManager(config_manager)

        # 采集线程
        self.collection_thread = None

        self.init_ui()
        self.load_settings()
        self.check_auth_status()
        self.check_login_status()  # 检查登录状态
        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("XY商品采集器 v4.0 - Python版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置应用程序样式
        self.setStyleSheet(self.get_app_style())
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # 创建各个区域
        self.create_search_area(main_layout)
        self.create_data_area(main_layout)
        self.create_control_area(main_layout)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_search_area(self, parent_layout):
        """创建搜索设置区域"""
        search_group = QGroupBox("📝 搜索设置")
        search_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        search_layout = QGridLayout(search_group)
        
        # 关键词搜索
        search_layout.addWidget(QLabel("关键词:"), 0, 0)
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("请输入搜索关键词，如：手机")
        search_layout.addWidget(self.keyword_input, 0, 1, 1, 2)
        
        self.search_btn = QPushButton("🔍 开始搜索")
        self.search_btn.clicked.connect(self.start_search_collection)
        search_layout.addWidget(self.search_btn, 0, 3)
        
        # 店铺链接
        search_layout.addWidget(QLabel("店铺链接:"), 1, 0)
        self.shop_input = QLineEdit()
        self.shop_input.setPlaceholderText("请输入店铺链接")
        search_layout.addWidget(self.shop_input, 1, 1, 1, 2)
        
        self.shop_btn = QPushButton("🛒 采集店铺")
        self.shop_btn.clicked.connect(self.start_shop_collection)
        search_layout.addWidget(self.shop_btn, 1, 3)
        
        # 过滤设置
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("想要人数≥"))
        self.min_want_spin = QSpinBox()
        self.min_want_spin.setRange(0, 999999)
        self.min_want_spin.setValue(10)
        filter_layout.addWidget(self.min_want_spin)
        
        filter_layout.addWidget(QLabel("采集页数:"))
        self.pages_spin = QSpinBox()
        self.pages_spin.setRange(1, 50)
        self.pages_spin.setValue(3)
        filter_layout.addWidget(self.pages_spin)
        
        self.detail_check = QCheckBox("详细模式")
        self.detail_check.setToolTip("开启后将采集更详细的商品信息")
        filter_layout.addWidget(self.detail_check)
        
        filter_layout.addStretch()
        search_layout.addLayout(filter_layout, 2, 0, 1, 4)
        
        parent_layout.addWidget(search_group)
    
    def create_data_area(self, parent_layout):
        """创建数据展示区域"""
        data_group = QGroupBox("📊 数据展示区域")
        data_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        data_layout = QVBoxLayout(data_group)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        self.stats_label = QLabel("已采集: 0条")
        self.stats_label.setFont(QFont("Microsoft YaHei", 9))
        stats_layout.addWidget(self.stats_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        stats_layout.addWidget(self.progress_bar)
        
        stats_layout.addStretch()
        data_layout.addLayout(stats_layout)
        
        # 数据表格
        self.data_table = QTableView()
        self.data_table.setModel(self.data_model)
        self.setup_table()
        data_layout.addWidget(self.data_table)
        
        parent_layout.addWidget(data_group)
    
    def setup_table(self):
        """设置数据表格"""
        # 设置表格属性
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableView.SelectRows)

        # 设置列宽（移除图片列）
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 序号
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 标题
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 价格
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 想要
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 浏览
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 转化率
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 链接

        self.data_table.setColumnWidth(0, 60)   # 序号
        self.data_table.setColumnWidth(2, 100)  # 价格
        self.data_table.setColumnWidth(3, 80)   # 想要
        self.data_table.setColumnWidth(4, 80)   # 浏览
        self.data_table.setColumnWidth(5, 80)   # 转化率
        self.data_table.setColumnWidth(6, 80)   # 状态
        self.data_table.setColumnWidth(7, 100)  # 链接
    
    def create_control_area(self, parent_layout):
        """创建控制按钮区域"""
        control_layout = QHBoxLayout()

        # 登录相关按钮
        self.login_btn = QPushButton("🔑 登录账号")
        self.login_btn.clicked.connect(self.show_login_browser)
        control_layout.addWidget(self.login_btn)

        self.login_status_label = QLabel("❌ 未登录")
        self.login_status_label.setStyleSheet("color: red; font-weight: bold;")
        control_layout.addWidget(self.login_status_label)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        control_layout.addWidget(separator)

        self.detail_show_btn = QPushButton("📋 详细展示")
        self.detail_show_btn.clicked.connect(self.show_detail_html)
        self.detail_show_btn.setEnabled(False)
        control_layout.addWidget(self.detail_show_btn)

        self.export_btn = QPushButton("📦 一键导出")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setEnabled(False)
        control_layout.addWidget(self.export_btn)

        self.clear_btn = QPushButton("🗑️ 清空数据")
        self.clear_btn.clicked.connect(self.clear_data)
        control_layout.addWidget(self.clear_btn)

        self.stop_btn = QPushButton("⏸️ 停止采集")
        self.stop_btn.clicked.connect(self.stop_collection)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        control_layout.addStretch()

        # 授权状态显示
        self.auth_status_label = QLabel("🔐 授权状态: 检查中...")
        control_layout.addWidget(self.auth_status_label)

        parent_layout.addLayout(control_layout)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def get_app_style(self):
        """获取应用程序样式"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 10px;
            background-color: white;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2c3e50;
        }
        
        QPushButton {
            background-color: #3498db;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            min-width: 100px;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        
        QPushButton:disabled {
            background-color: #bdc3c7;
        }
        
        QLineEdit {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            font-size: 12px;
        }
        
        QLineEdit:focus {
            border-color: #3498db;
        }
        
        QTableWidget {
            gridline-color: #ddd;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
        }
        """
    
    def load_settings(self):
        """加载用户设置"""
        try:
            # 加载窗口大小
            width = self.config_manager.get('ui.window_width', 1200)
            height = self.config_manager.get('ui.window_height', 800)
            self.resize(width, height)
            
            # 加载采集设置
            self.min_want_spin.setValue(self.config_manager.get('collection.min_want_count', 10))
            self.pages_spin.setValue(self.config_manager.get('collection.max_pages', 3))
            self.detail_check.setChecked(self.config_manager.get('collection.detail_mode', False))
            
            self.logger.info("用户设置加载完成")
        except Exception as e:
            self.logger.error(f"加载用户设置失败: {e}")
    
    def save_settings(self):
        """保存用户设置"""
        try:
            # 保存窗口大小
            self.config_manager.set('ui.window_width', self.width())
            self.config_manager.set('ui.window_height', self.height())
            
            # 保存采集设置
            self.config_manager.set('collection.min_want_count', self.min_want_spin.value())
            self.config_manager.set('collection.max_pages', self.pages_spin.value())
            self.config_manager.set('collection.detail_mode', self.detail_check.isChecked())
            
            self.logger.info("用户设置保存完成")
        except Exception as e:
            self.logger.error(f"保存用户设置失败: {e}")
    
    def start_search_collection(self):
        """开始搜索采集"""
        keyword = self.keyword_input.text().strip()
        if not keyword:
            QMessageBox.warning(self, "警告", "请输入搜索关键词！")
            return

        if self.is_collecting:
            QMessageBox.warning(self, "警告", "正在采集中，请稍候...")
            return

        # 检查登录状态
        if not self.check_login_status():
            QMessageBox.warning(self, "登录提醒", "请先登录账号再进行采集！")
            return

        self.logger.info(f"🚀 开始搜索采集: {keyword}")

        # 清空之前的数据
        self.collected_data.clear()
        self.data_model.update_data([])
        self.stats_label.setText("准备采集...")

        # 准备采集参数
        params = {
            'keyword': keyword,
            'max_pages': self.pages_spin.value(),
            'min_want_count': self.min_want_spin.value()
        }

        self.start_collection('search', **params)

    def start_shop_collection(self):
        """开始店铺采集"""
        shop_url = self.shop_input.text().strip()
        if not shop_url:
            QMessageBox.warning(self, "警告", "请输入店铺链接！")
            return

        if self.is_collecting:
            QMessageBox.warning(self, "警告", "正在采集中，请稍候...")
            return

        # 检查登录状态
        if not self.check_login_status():
            QMessageBox.warning(self, "登录提醒", "请先登录账号再进行采集！")
            return

        self.logger.info(f"🏪 开始店铺采集: {shop_url}")

        # 清空之前的数据
        self.collected_data.clear()
        self.data_model.update_data([])
        self.stats_label.setText("准备采集...")

        # 准备采集参数
        params = {
            'shop_url': shop_url,
            'max_items': 0,  # 不限制数量
            'min_want_count': self.min_want_spin.value()
        }

        self.start_collection('shop', **params)
    
    def show_detail_html(self):
        """显示详细HTML"""
        if not self.collected_data:
            QMessageBox.warning(self, "警告", "没有可展示的数据！")
            return

        try:
            # 生成HTML文件
            html_file = self.export_manager.export_to_html(self.collected_data, "XY商品数据详细展示")

            # 在浏览器中打开
            if self.export_manager.open_in_browser(html_file):
                QMessageBox.information(self, "成功", "详细数据已在浏览器中打开！")
            else:
                QMessageBox.warning(self, "警告", f"无法打开浏览器，文件已保存至：\n{html_file}")

        except Exception as e:
            self.logger.error(f"详细展示失败: {e}")
            QMessageBox.critical(self, "错误", f"详细展示失败：{str(e)}")

    def export_data(self):
        """导出数据"""
        if not self.collected_data:
            QMessageBox.warning(self, "警告", "没有可导出的数据！")
            return

        try:
            # 询问导出类型
            reply = QMessageBox.question(
                self, "选择导出类型",
                "请选择导出类型：\n\n"
                "Yes - 导出Excel表格\n"
                "No - 导出图片压缩包\n"
                "Cancel - 导出完整数据包",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )

            if reply == QMessageBox.Yes:
                # 导出Excel
                excel_file = self.export_manager.export_to_excel(self.collected_data, "XY商品数据")
                QMessageBox.information(self, "成功", f"Excel文件已导出：\n{excel_file}")

            elif reply == QMessageBox.No:
                # 导出图片包
                zip_file = self.export_manager.export_images(self.collected_data, "XY商品图片")
                QMessageBox.information(self, "成功", f"图片压缩包已导出：\n{zip_file}")

            elif reply == QMessageBox.Cancel:
                # 导出完整包
                complete_file = self.export_manager.export_complete_package(self.collected_data, "XY商品完整数据")
                QMessageBox.information(self, "成功", f"完整数据包已导出：\n{complete_file}")

        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            QMessageBox.critical(self, "错误", f"数据导出失败：{str(e)}")
    
    def start_collection(self, collection_type: str, **kwargs):
        """开始采集任务"""
        try:
            # 设置UI状态
            self.is_collecting = True
            self.search_btn.setEnabled(False)
            self.shop_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_bar.showMessage("正在采集数据...")

            # 创建并启动采集线程
            self.collection_thread = CollectionThread(
                self.collector_engine, collection_type, **kwargs
            )
            self.collection_thread.progress_updated.connect(self.on_progress_updated)
            self.collection_thread.data_collected.connect(self.on_data_collected)
            self.collection_thread.error_occurred.connect(self.on_collection_error)
            self.collection_thread.finished.connect(self.on_collection_finished)

            self.collection_thread.start()

        except Exception as e:
            self.logger.error(f"启动采集失败: {e}")
            QMessageBox.critical(self, "错误", f"启动采集失败: {str(e)}")
            self.on_collection_finished()

    def on_progress_updated(self, progress_info: dict):
        """更新采集进度"""
        try:
            current_page = progress_info.get('current_page', 0)
            total_pages = progress_info.get('total_pages', 1)
            current_items = progress_info.get('current_items', 0)
            total_items = progress_info.get('total_items', 0)
            progress = progress_info.get('progress', 0)

            self.progress_bar.setValue(progress)

            # 更新统计信息
            if total_pages == 999:  # 店铺采集（未知总页数）
                self.stats_label.setText(f"已采集: {total_items}条 (第{current_page}页)")
                self.status_bar.showMessage(f"正在采集店铺第{current_page}页，当前页{current_items}条...")
            else:  # 搜索采集
                self.stats_label.setText(f"已采集: {total_items}条 (第{current_page}/{total_pages}页)")
                self.status_bar.showMessage(f"正在搜索第{current_page}页，当前页{current_items}条...")

            # 强制刷新界面
            QApplication.processEvents()

        except Exception as e:
            self.logger.error(f"更新进度失败: {e}")

    def on_data_collected(self, raw_data: list):
        """处理采集到的数据"""
        try:
            # 数据清洗和验证
            cleaned_data = self.data_processor.clean_and_validate(raw_data)

            # 更新数据模型
            self.collected_data = cleaned_data
            self.data_model.update_data(cleaned_data)

            # 更新统计信息
            self.update_statistics()

            # 启用导出按钮
            if cleaned_data:
                self.detail_show_btn.setEnabled(True)
                self.export_btn.setEnabled(True)

                # 显示成功消息
                QMessageBox.information(
                    self,
                    "采集完成",
                    f"🎉 数据采集完成！\n\n"
                    f"✅ 共获得 {len(cleaned_data)} 条有效数据\n"
                    f"📊 您可以查看详细信息或导出数据"
                )
            else:
                QMessageBox.warning(
                    self,
                    "采集完成",
                    "⚠️ 采集完成，但没有获得符合条件的数据\n\n"
                    "💡 建议：\n"
                    "- 降低想要人数过滤条件\n"
                    "- 尝试其他关键词\n"
                    "- 检查网络连接"
                )

            self.logger.info(f"数据采集完成，共{len(cleaned_data)}条有效数据")

        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            QMessageBox.critical(self, "错误", f"数据处理失败: {str(e)}")

    def on_collection_error(self, error_message: str):
        """处理采集错误"""
        self.logger.error(f"采集错误: {error_message}")

        # 提供更友好的错误信息
        if "网络" in error_message or "连接" in error_message:
            error_msg = f"🌐 网络连接错误\n\n{error_message}\n\n💡 建议：\n- 检查网络连接\n- 稍后重试"
        elif "API" in error_message or "接口" in error_message:
            error_msg = f"🔌 API接口错误\n\n{error_message}\n\n💡 建议：\n- 检查API地址是否正确\n- 稍后重试"
        elif "解析" in error_message:
            error_msg = f"📊 数据解析错误\n\n{error_message}\n\n💡 建议：\n- 检查数据格式\n- 联系技术支持"
        else:
            error_msg = f"❌ 采集错误\n\n{error_message}"

        QMessageBox.critical(self, "采集错误", error_msg)

    def on_collection_finished(self):
        """采集完成"""
        self.is_collecting = False
        self.search_btn.setEnabled(True)
        self.shop_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)

        # 更新状态栏
        if self.collected_data:
            self.status_bar.showMessage(f"采集完成 - 共获得 {len(self.collected_data)} 条数据")
        else:
            self.status_bar.showMessage("采集完成 - 无有效数据")

        if self.collection_thread:
            self.collection_thread.quit()
            self.collection_thread.wait()
            self.collection_thread = None

    def update_statistics(self):
        """更新统计信息"""
        stats = self.data_processor.get_statistics(self.collected_data)
        stats_text = f"已采集: {stats['total_count']}条 | " \
                    f"在售: {stats['available_count']}条 | " \
                    f"已售: {stats['sold_count']}条 | " \
                    f"平均价格: ¥{stats['avg_price']:.2f}"
        self.stats_label.setText(stats_text)

    def clear_data(self):
        """清空数据"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有数据吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.collected_data.clear()
            self.data_model.update_data([])
            self.stats_label.setText("已采集: 0条")
            self.detail_show_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
            self.logger.info("数据已清空")

    def stop_collection(self):
        """停止采集"""
        if self.is_collecting and self.collector_engine:
            self.collector_engine.stop_collection()
            self.logger.info("用户停止采集")

    def check_auth_status(self):
        """检查授权状态"""
        # 调试模式：显示调试状态
        self.auth_status_label.setText("🔧 调试模式: 授权已跳过")
        self.auth_status_label.setStyleSheet("color: orange;")
        self.logger.info("🔧 调试模式：授权状态检查跳过")
    
    def show_login_browser(self):
        """显示登录浏览器窗口"""
        try:
            self.logger.info("打开登录浏览器...")
            self.login_btn.setEnabled(False)
            self.login_btn.setText("🔄 登录中...")

            # 在新线程中打开登录浏览器
            self.login_thread = LoginThread(self.collector_engine)
            self.login_thread.login_success.connect(self.on_login_success)
            self.login_thread.login_failed.connect(self.on_login_failed)
            self.login_thread.start()

        except Exception as e:
            self.logger.error(f"打开登录浏览器失败: {e}")
            QMessageBox.critical(self, "错误", f"打开登录浏览器失败: {e}")
            self.login_btn.setEnabled(True)
            self.login_btn.setText("🔑 登录账号")

    def on_login_success(self):
        """登录成功回调"""
        self.login_status_label.setText("✅ 已登录")
        self.login_status_label.setStyleSheet("color: green; font-weight: bold;")
        self.login_btn.setText("🔑 重新登录")
        self.login_btn.setEnabled(True)
        self.logger.info("登录成功")
        QMessageBox.information(self, "成功", "登录成功！现在可以开始采集了。")

    def on_login_failed(self, error_msg):
        """登录失败回调"""
        self.login_status_label.setText("❌ 登录失败")
        self.login_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.login_btn.setText("🔑 登录账号")
        self.login_btn.setEnabled(True)
        self.logger.error(f"登录失败: {error_msg}")
        QMessageBox.warning(self, "登录失败", f"登录失败: {error_msg}")

    def check_login_status(self):
        """检查登录状态"""
        try:
            # 检查cookies文件是否有有效的登录信息
            if self.collector_engine.has_valid_cookies():
                self.login_status_label.setText("✅ 已登录")
                self.login_status_label.setStyleSheet("color: green; font-weight: bold;")
                self.login_btn.setText("🔑 重新登录")
                return True
            else:
                self.login_status_label.setText("❌ 未登录")
                self.login_status_label.setStyleSheet("color: red; font-weight: bold;")
                self.login_btn.setText("🔑 登录账号")
                return False
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            self.login_status_label.setText("❓ 状态未知")
            self.login_status_label.setStyleSheet("color: orange; font-weight: bold;")
            return False

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止采集
        if self.is_collecting:
            self.stop_collection()

        # 关闭采集引擎
        if self.collector_engine:
            self.collector_engine.close()

        # 保存设置
        self.save_settings()
        event.accept()


class LoginThread(QThread):
    """登录线程"""
    login_success = pyqtSignal()
    login_failed = pyqtSignal(str)

    def __init__(self, collector_engine):
        super().__init__()
        self.collector_engine = collector_engine
        self.logger = get_logger(self.__class__.__name__)

    def run(self):
        """运行登录流程"""
        try:
            self.logger.info("开始登录流程...")
            # 使用小窗口浏览器进行登录
            success = self.collector_engine.login_with_small_browser()
            if success:
                self.login_success.emit()
            else:
                self.login_failed.emit("登录失败或超时")
        except Exception as e:
            self.logger.error(f"登录线程异常: {e}")
            self.login_failed.emit(str(e))
